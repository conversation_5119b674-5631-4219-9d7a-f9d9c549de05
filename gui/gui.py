import sys
import os
import logging
import subprocess
import platform
from datetime import datetime
from PyQt5.QtWidgets import (
    QA<PERSON>lication, QMainWindow, QW<PERSON>t, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QLineEdit, QPushButton, QListWidget, QListWidgetItem,
    QTextEdit, QCheckBox, QFileDialog, QMessageBox, QProgressBar,
    QFrame, QSplitter, QGroupBox, QGridLayout, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

from tasks.task_manager import TaskManager, TaskStatus, Task
from server.api import APIServer
from processing.downloader import YouTubeDownloader
from processing.processor import AudioProcessor

class ProcessingWorker(QThread):
    """Worker thread for processing tasks"""
    finished = pyqtSignal(str, bool, str)  # task_id, success, message
    progress = pyqtSignal(str, str)  # task_id, status_message
    
    def __init__(self, task_id: str, url: str, title: str):
        super().__init__()
        self.task_id = task_id
        self.url = url
        self.title = title
        self.downloader = YouTubeDownloader()
        self.processor = AudioProcessor()
    
    def run(self):
        try:
            # Download phase
            self.progress.emit(self.task_id, "جاري تحميل الصوت...")
            success, audio_file, error_msg = self.downloader.download_audio(self.url, self.task_id)
            
            if not success:
                self.finished.emit(self.task_id, False, error_msg)
                return
            
            # Processing phase
            self.progress.emit(self.task_id, "جاري معالجة الصوت...")
            success, vocals_file, error_msg = self.processor.process_audio(audio_file, self.task_id, self.title)
            
            if success:
                self.finished.emit(self.task_id, True, vocals_file)
            else:
                self.finished.emit(self.task_id, False, error_msg)
                
            # Cleanup
            self.downloader.cleanup_temp_files(self.task_id)
            
        except Exception as e:
            self.finished.emit(self.task_id, False, f"خطأ غير متوقع: {str(e)}")

class TaskWidget(QWidget):
    """Custom widget for displaying a single task"""
    delete_requested = pyqtSignal(str)  # task_id
    
    def __init__(self, task: Task):
        super().__init__()
        self.task = task
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Main frame
        frame = QFrame()
        frame.setFrameStyle(QFrame.Box)
        frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: #f9f9f9;
                padding: 10px;
            }
        """)
        
        frame_layout = QVBoxLayout(frame)
        
        # Title and URL
        title_label = QLabel(self.task.title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setWordWrap(True)
        frame_layout.addWidget(title_label)
        
        url_label = QLabel(f"الرابط: {self.task.url}")
        url_label.setFont(QFont("Arial", 9))
        url_label.setStyleSheet("color: #666;")
        url_label.setWordWrap(True)
        frame_layout.addWidget(url_label)
        
        # Status
        self.status_label = QLabel()
        self.update_status_display()
        frame_layout.addWidget(self.status_label)
        
        # Progress bar (only shown during processing)
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.progress_bar.setVisible(self.task.status == TaskStatus.PROCESSING)
        frame_layout.addWidget(self.progress_bar)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        # Play vocals button
        self.play_btn = QPushButton("🎵 فتح الصوت المعالج")
        self.play_btn.clicked.connect(self.play_vocals)
        self.play_btn.setEnabled(self.task.status == TaskStatus.COMPLETED and self.task.vocals_file)
        buttons_layout.addWidget(self.play_btn)
        
        # Open folder button
        self.folder_btn = QPushButton("📁 فتح المجلد")
        self.folder_btn.clicked.connect(self.open_folder)
        self.folder_btn.setEnabled(self.task.status == TaskStatus.COMPLETED and self.task.output_folder)
        buttons_layout.addWidget(self.folder_btn)
        
        # Open URL button
        url_btn = QPushButton("🔗 فتح رابط الفيديو")
        url_btn.clicked.connect(self.open_url)
        buttons_layout.addWidget(url_btn)
        
        # Delete button
        delete_btn = QPushButton("🗑️ حذف المهمة")
        delete_btn.clicked.connect(self.delete_task)
        delete_btn.setStyleSheet("QPushButton { color: red; }")
        buttons_layout.addWidget(delete_btn)
        
        frame_layout.addLayout(buttons_layout)
        layout.addWidget(frame)
        self.setLayout(layout)
    
    def update_status_display(self):
        """Update the status label based on task status"""
        status_text = ""
        style = ""
        
        if self.task.status == TaskStatus.PENDING:
            status_text = "⏳ في الانتظار"
            style = "color: orange;"
        elif self.task.status == TaskStatus.PROCESSING:
            status_text = "⚙️ جاري المعالجة..."
            style = "color: blue;"
        elif self.task.status == TaskStatus.COMPLETED:
            status_text = "✅ تمت المعالجة"
            style = "color: green;"
        elif self.task.status == TaskStatus.FAILED:
            status_text = f"❌ فشل: {self.task.error_message}"
            style = "color: red;"
        
        self.status_label.setText(status_text)
        self.status_label.setStyleSheet(style)
        
        # Update progress bar visibility
        self.progress_bar.setVisible(self.task.status == TaskStatus.PROCESSING)
        
        # Update button states
        completed = self.task.status == TaskStatus.COMPLETED
        self.play_btn.setEnabled(completed and bool(self.task.vocals_file))
        self.folder_btn.setEnabled(completed and bool(self.task.output_folder))
    
    def play_vocals(self):
        """Play the vocals file"""
        if self.task.vocals_file and os.path.exists(self.task.vocals_file):
            try:
                if platform.system() == "Windows":
                    os.startfile(self.task.vocals_file)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", self.task.vocals_file])
                else:  # Linux
                    subprocess.run(["xdg-open", self.task.vocals_file])
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في فتح الملف: {str(e)}")
    
    def open_folder(self):
        """Open the output folder"""
        if self.task.output_folder and os.path.exists(self.task.output_folder):
            try:
                if platform.system() == "Windows":
                    os.startfile(self.task.output_folder)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", self.task.output_folder])
                else:  # Linux
                    subprocess.run(["xdg-open", self.task.output_folder])
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في فتح المجلد: {str(e)}")
    
    def open_url(self):
        """Open the YouTube URL in browser"""
        try:
            import webbrowser
            webbrowser.open(self.task.url)
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح الرابط: {str(e)}")
    
    def delete_task(self):
        """Request task deletion"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذه المهمة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.delete_requested.emit(self.task.id)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("No Music Desktop - إزالة الموسيقى من الفيديوهات")
        self.setGeometry(100, 100, 1200, 800)

        # Initialize components
        self.task_manager = TaskManager()
        self.api_server = APIServer(self.task_manager)
        self.downloader = YouTubeDownloader()
        self.processor = AudioProcessor()

        # Worker threads
        self.workers = {}

        # Setup logging
        self.setup_logging()

        # Setup UI
        self.setup_ui()
        self.setup_connections()

        # Load settings
        self.load_settings()

    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(log_dir, 'app.log'), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

        self.logger = logging.getLogger(__name__)

    def setup_ui(self):
        """Setup the main user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)

        # Create tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Create tabs
        self.create_main_tab()
        self.create_server_tab()
        self.create_settings_tab()

        # Apply RTL support
        self.setLayoutDirection(Qt.RightToLeft)

        # Apply styling
        self.apply_styling()

    def create_main_tab(self):
        """Create the main processing tab"""
        main_tab = QWidget()
        layout = QVBoxLayout(main_tab)

        # URL input section
        input_group = QGroupBox("إدخال رابط YouTube")
        input_layout = QHBoxLayout(input_group)

        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("أدخل رابط YouTube هنا...")
        self.url_input.returnPressed.connect(self.process_url)
        input_layout.addWidget(self.url_input)

        self.process_btn = QPushButton("معالجة")
        self.process_btn.clicked.connect(self.process_url)
        self.process_btn.setMinimumWidth(100)
        input_layout.addWidget(self.process_btn)

        layout.addWidget(input_group)

        # Tasks section
        tasks_group = QGroupBox("قائمة المهام")
        tasks_layout = QVBoxLayout(tasks_group)

        # Tasks scroll area
        self.tasks_scroll = QScrollArea()
        self.tasks_scroll.setWidgetResizable(True)
        self.tasks_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.tasks_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.tasks_container = QWidget()
        self.tasks_layout = QVBoxLayout(self.tasks_container)
        self.tasks_layout.addStretch()

        self.tasks_scroll.setWidget(self.tasks_container)
        tasks_layout.addWidget(self.tasks_scroll)

        layout.addWidget(tasks_group)

        self.tab_widget.addTab(main_tab, "المعالجة الرئيسية")

    def create_server_tab(self):
        """Create the server management tab"""
        server_tab = QWidget()
        layout = QVBoxLayout(server_tab)

        # Server control section
        control_group = QGroupBox("التحكم في الخادم")
        control_layout = QVBoxLayout(control_group)

        # Server status
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("حالة الخادم:"))

        self.server_status_label = QLabel("متوقف")
        self.server_status_label.setStyleSheet("color: red; font-weight: bold;")
        status_layout.addWidget(self.server_status_label)
        status_layout.addStretch()

        control_layout.addLayout(status_layout)

        # Server buttons
        buttons_layout = QHBoxLayout()

        self.start_server_btn = QPushButton("تشغيل الخادم")
        self.start_server_btn.clicked.connect(self.start_server)
        buttons_layout.addWidget(self.start_server_btn)

        self.stop_server_btn = QPushButton("إيقاف الخادم")
        self.stop_server_btn.clicked.connect(self.stop_server)
        self.stop_server_btn.setEnabled(False)
        buttons_layout.addWidget(self.stop_server_btn)

        buttons_layout.addStretch()
        control_layout.addLayout(buttons_layout)

        # Port info
        port_label = QLabel(f"المنفذ: {self.api_server.port}")
        control_layout.addWidget(port_label)

        layout.addWidget(control_group)

        # Server logs section
        logs_group = QGroupBox("سجلات الخادم")
        logs_layout = QVBoxLayout(logs_group)

        self.server_logs = QTextEdit()
        self.server_logs.setReadOnly(True)
        self.server_logs.setMaximumBlockCount(1000)  # Limit log entries
        logs_layout.addWidget(self.server_logs)

        # Clear logs button
        clear_logs_btn = QPushButton("مسح السجلات")
        clear_logs_btn.clicked.connect(self.server_logs.clear)
        logs_layout.addWidget(clear_logs_btn)

        layout.addWidget(logs_group)

        self.tab_widget.addTab(server_tab, "خادم الإضافة")

    def create_settings_tab(self):
        """Create the settings tab"""
        settings_tab = QWidget()
        layout = QVBoxLayout(settings_tab)

        # Output directory setting
        output_group = QGroupBox("إعدادات المخرجات")
        output_layout = QVBoxLayout(output_group)

        dir_layout = QHBoxLayout()
        dir_layout.addWidget(QLabel("مجلد المخرجات:"))

        self.output_dir_label = QLabel("data/processed")
        dir_layout.addWidget(self.output_dir_label)

        change_dir_btn = QPushButton("تغيير")
        change_dir_btn.clicked.connect(self.change_output_directory)
        dir_layout.addWidget(change_dir_btn)

        output_layout.addLayout(dir_layout)
        layout.addWidget(output_group)

        # Auto-start server setting
        auto_group = QGroupBox("إعدادات التشغيل التلقائي")
        auto_layout = QVBoxLayout(auto_group)

        self.auto_start_checkbox = QCheckBox("تشغيل الخادم تلقائياً عند بدء البرنامج")
        auto_layout.addWidget(self.auto_start_checkbox)

        layout.addWidget(auto_group)

        # Language setting
        lang_group = QGroupBox("إعدادات اللغة")
        lang_layout = QVBoxLayout(lang_group)

        lang_label = QLabel("اللغة الحالية: العربية")
        lang_layout.addWidget(lang_label)

        layout.addWidget(lang_group)

        # Save settings button
        save_settings_btn = QPushButton("حفظ الإعدادات")
        save_settings_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_settings_btn)

        layout.addStretch()

        self.tab_widget.addTab(settings_tab, "الإعدادات")

    def setup_connections(self):
        """Setup signal connections"""
        # Task manager signals
        self.task_manager.task_added.connect(self.on_task_added)
        self.task_manager.task_updated.connect(self.on_task_updated)
        self.task_manager.task_removed.connect(self.on_task_removed)

        # API server signals
        self.api_server.log_message.connect(self.add_server_log)
        self.api_server.server_status_changed.connect(self.on_server_status_changed)

    def apply_styling(self):
        """Apply custom styling to the application"""
        style = """
        QMainWindow {
            background-color: #f5f5f5;
        }
        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: white;
        }
        QTabBar::tab {
            background-color: #e0e0e0;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #007acc;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #005a9e;
        }
        QPushButton:pressed {
            background-color: #004578;
        }
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        QLineEdit {
            padding: 8px;
            border: 2px solid #cccccc;
            border-radius: 4px;
            font-size: 12px;
        }
        QLineEdit:focus {
            border-color: #007acc;
        }
        """
        self.setStyleSheet(style)

    def process_url(self):
        """Process a YouTube URL"""
        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رابط YouTube")
            return

        # Validate URL
        if not self.downloader.validate_url(url):
            QMessageBox.warning(self, "خطأ", "رابط YouTube غير صحيح")
            return

        # Get video info
        video_info = self.downloader.get_video_info(url)
        if not video_info:
            QMessageBox.warning(self, "خطأ", "فشل في الحصول على معلومات الفيديو")
            return

        # Create task
        title = video_info.get('title', 'Unknown')
        task_id = self.task_manager.add_task(url, title)

        # Start processing
        self.start_processing(task_id, url, title)

        # Clear input
        self.url_input.clear()

    def start_processing(self, task_id: str, url: str, title: str):
        """Start processing a task in background"""
        # Update task status
        self.task_manager.update_task_status(task_id, TaskStatus.PROCESSING)

        # Create and start worker thread
        worker = ProcessingWorker(task_id, url, title)
        worker.finished.connect(self.on_processing_finished)
        worker.progress.connect(self.on_processing_progress)

        self.workers[task_id] = worker
        worker.start()

    def on_processing_finished(self, task_id: str, success: bool, message: str):
        """Handle processing completion"""
        if success:
            # Update task with results
            vocals_file = message  # message contains the vocals file path
            output_folder = os.path.dirname(vocals_file)
            self.task_manager.update_task_output(task_id, output_folder, vocals_file)
            self.task_manager.update_task_status(task_id, TaskStatus.COMPLETED)

            self.logger.info(f"Task completed successfully: {task_id}")
        else:
            # Update task with error
            self.task_manager.update_task_status(task_id, TaskStatus.FAILED, message)
            self.logger.error(f"Task failed: {task_id} - {message}")

        # Clean up worker
        if task_id in self.workers:
            del self.workers[task_id]

    def on_processing_progress(self, task_id: str, status_message: str):
        """Handle processing progress updates"""
        self.logger.info(f"Task {task_id}: {status_message}")

    def on_task_added(self, task_id: str):
        """Handle new task addition"""
        task = self.task_manager.get_task(task_id)
        if task:
            self.add_task_widget(task)

    def on_task_updated(self, task_id: str):
        """Handle task updates"""
        self.refresh_task_widgets()

    def on_task_removed(self, task_id: str):
        """Handle task removal"""
        self.refresh_task_widgets()

    def add_task_widget(self, task: Task):
        """Add a task widget to the UI"""
        task_widget = TaskWidget(task)
        task_widget.delete_requested.connect(self.delete_task)

        # Insert at the beginning (newest first)
        self.tasks_layout.insertWidget(0, task_widget)

    def refresh_task_widgets(self):
        """Refresh all task widgets"""
        # Clear existing widgets
        for i in reversed(range(self.tasks_layout.count())):
            child = self.tasks_layout.itemAt(i).widget()
            if child and isinstance(child, TaskWidget):
                child.setParent(None)

        # Add all tasks
        tasks = self.task_manager.get_all_tasks()
        for task in tasks:
            self.add_task_widget(task)

    def delete_task(self, task_id: str):
        """Delete a task"""
        # Stop worker if running
        if task_id in self.workers:
            worker = self.workers[task_id]
            worker.terminate()
            worker.wait()
            del self.workers[task_id]

        # Remove task
        self.task_manager.remove_task(task_id)

        # Clean up files
        self.processor.cleanup_task_files(task_id)

    def start_server(self):
        """Start the API server"""
        try:
            self.api_server.start_server()
            self.add_server_log("تم تشغيل الخادم بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تشغيل الخادم: {str(e)}")

    def stop_server(self):
        """Stop the API server"""
        try:
            self.api_server.stop_server()
            self.add_server_log("تم إيقاف الخادم")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إيقاف الخادم: {str(e)}")

    def on_server_status_changed(self, is_running: bool):
        """Handle server status changes"""
        if is_running:
            self.server_status_label.setText("يعمل")
            self.server_status_label.setStyleSheet("color: green; font-weight: bold;")
            self.start_server_btn.setEnabled(False)
            self.stop_server_btn.setEnabled(True)
        else:
            self.server_status_label.setText("متوقف")
            self.server_status_label.setStyleSheet("color: red; font-weight: bold;")
            self.start_server_btn.setEnabled(True)
            self.stop_server_btn.setEnabled(False)

    def add_server_log(self, message: str):
        """Add a message to server logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.server_logs.append(log_entry)

        # Auto-scroll to bottom
        scrollbar = self.server_logs.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def change_output_directory(self):
        """Change the output directory"""
        current_dir = self.output_dir_label.text()
        new_dir = QFileDialog.getExistingDirectory(
            self, "اختر مجلد المخرجات", current_dir
        )

        if new_dir:
            self.output_dir_label.setText(new_dir)
            # Update processor output directory
            self.processor.output_dir = new_dir

    def save_settings(self):
        """Save application settings"""
        try:
            settings = {
                'output_directory': self.output_dir_label.text(),
                'auto_start_server': self.auto_start_checkbox.isChecked(),
            }

            settings_file = os.path.join("data", "settings.json")
            os.makedirs(os.path.dirname(settings_file), exist_ok=True)

            import json
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def load_settings(self):
        """Load application settings"""
        try:
            settings_file = os.path.join("data", "settings.json")
            if os.path.exists(settings_file):
                import json
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # Apply settings
                output_dir = settings.get('output_directory', 'data/processed')
                self.output_dir_label.setText(output_dir)
                self.processor.output_dir = output_dir

                auto_start = settings.get('auto_start_server', False)
                self.auto_start_checkbox.setChecked(auto_start)

                # Auto-start server if enabled
                if auto_start:
                    QTimer.singleShot(1000, self.start_server)  # Delay to ensure UI is ready

        except Exception as e:
            self.logger.error(f"Failed to load settings: {e}")

    def closeEvent(self, event):
        """Handle application close event"""
        # Stop server if running
        if self.api_server.is_running:
            self.api_server.stop_server()

        # Stop all worker threads
        for worker in self.workers.values():
            worker.terminate()
            worker.wait()

        # Save settings
        try:
            self.save_settings()
        except:
            pass  # Don't prevent closing if settings save fails

        event.accept()

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("No Music Desktop")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("No Music")

    # Set RTL layout direction for Arabic support
    app.setLayoutDirection(Qt.RightToLeft)

    # Create and show main window
    window = MainWindow()
    window.show()

    # Load existing tasks
    window.refresh_task_widgets()

    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
