#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for basic functionality without GUI
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    
    try:
        from tasks.task_manager import TaskManager, TaskStatus
        print("✅ TaskManager imported successfully")
    except Exception as e:
        print(f"❌ Failed to import TaskManager: {e}")
        return False
    
    try:
        from processing.downloader import YouTubeDownloader
        print("✅ YouTubeDownloader imported successfully")
    except Exception as e:
        print(f"❌ Failed to import YouTubeDownloader: {e}")
        return False
    
    try:
        from processing.processor import AudioProcessor
        print("✅ AudioProcessor imported successfully")
    except Exception as e:
        print(f"❌ Failed to import AudioProcessor: {e}")
        return False
    
    try:
        from server.api import APIServer
        print("✅ APIServer imported successfully")
    except Exception as e:
        print(f"❌ Failed to import APIServer: {e}")
        return False
    
    return True

def test_task_manager():
    """Test TaskManager functionality"""
    print("\nTesting TaskManager...")
    
    try:
        # Create task manager
        tm = TaskManager("data/test")
        
        # Add a test task
        task_id = tm.add_task("https://youtube.com/watch?v=test", "Test Video")
        print(f"✅ Task created with ID: {task_id}")
        
        # Get task
        task = tm.get_task(task_id)
        if task:
            print(f"✅ Task retrieved: {task.title}")
        else:
            print("❌ Failed to retrieve task")
            return False
        
        # Update task status
        tm.update_task_status(task_id, TaskStatus.PROCESSING)
        print("✅ Task status updated")
        
        # Get all tasks
        tasks = tm.get_all_tasks()
        print(f"✅ Total tasks: {len(tasks)}")
        
        # Remove task
        tm.remove_task(task_id)
        print("✅ Task removed")
        
        return True
        
    except Exception as e:
        print(f"❌ TaskManager test failed: {e}")
        return False

def test_downloader():
    """Test YouTubeDownloader functionality"""
    print("\nTesting YouTubeDownloader...")
    
    try:
        downloader = YouTubeDownloader()
        
        # Test URL validation
        valid_url = "https://youtube.com/watch?v=dQw4w9WgXcQ"
        invalid_url = "https://example.com"
        
        if downloader.validate_url(valid_url):
            print("✅ Valid URL recognized")
        else:
            print("❌ Valid URL not recognized")
            return False
        
        if not downloader.validate_url(invalid_url):
            print("✅ Invalid URL rejected")
        else:
            print("❌ Invalid URL accepted")
            return False
        
        # Test video ID extraction
        video_id = downloader.extract_video_id(valid_url)
        if video_id == "dQw4w9WgXcQ":
            print("✅ Video ID extracted correctly")
        else:
            print(f"❌ Video ID extraction failed: {video_id}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ YouTubeDownloader test failed: {e}")
        return False

def test_processor():
    """Test AudioProcessor functionality"""
    print("\nTesting AudioProcessor...")
    
    try:
        processor = AudioProcessor("data/test_processed")
        
        # Test basic functionality
        print("✅ AudioProcessor created")
        
        # Test file validation
        if not processor.validate_audio_file("nonexistent.wav"):
            print("✅ Non-existent file validation works")
        else:
            print("❌ Non-existent file validation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ AudioProcessor test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("No Music Desktop - Basic Functionality Test")
    print("=" * 50)
    
    # Create test directories
    os.makedirs("data/test", exist_ok=True)
    os.makedirs("data/test_processed", exist_ok=True)
    
    tests = [
        test_imports,
        test_task_manager,
        test_downloader,
        test_processor
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Basic functionality is working.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
