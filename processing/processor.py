import os
import logging
import shutil
from typing import Tuple, Optional
import tempfile

# Try to import audio processing libraries
try:
    import librosa
    import soundfile as sf
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False

try:
    from spleeter.separator import Separator
    SPLEETER_AVAILABLE = True
except ImportError:
    SPLEETER_AVAILABLE = False

class AudioProcessor:
    def __init__(self, output_dir: str = "data/processed"):
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize Spleeter separator
        self.separator = None
        if SPLEETER_AVAILABLE:
            try:
                self.separator = Separator('spleeter:2stems-16kHz')
                self.logger.info("Spleeter separator initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize Spleeter: {e}")
                self.separator = None
        else:
            self.logger.warning("Spleeter not available - audio separation will not work")
    
    def process_audio(self, input_file: str, task_id: str, title: str) -> Tuple[bool, str, str]:
        """
        Process audio file to separate vocals
        Returns: (success, vocals_file_path, error_message)
        """
        if not SPLEETER_AVAILABLE:
            return False, "", "Spleeter غير متاح - يرجى تثبيت المكتبات المطلوبة"

        if not LIBROSA_AVAILABLE:
            return False, "", "Librosa غير متاح - يرجى تثبيت المكتبات المطلوبة"

        if not self.separator:
            return False, "", "فشل في تهيئة Spleeter"
        
        try:
            # Create task-specific output directory
            task_output_dir = os.path.join(self.output_dir, task_id)
            os.makedirs(task_output_dir, exist_ok=True)
            
            # Load audio file
            self.logger.info(f"Loading audio file: {input_file}")
            waveform, sample_rate = librosa.load(input_file, sr=None, mono=False)
            
            # Ensure stereo format for Spleeter
            if len(waveform.shape) == 1:
                # Convert mono to stereo
                waveform = librosa.to_mono(waveform)
                waveform = librosa.util.fix_length(waveform, size=len(waveform))
                waveform = librosa.util.stack([waveform, waveform], axis=0).T
            elif len(waveform.shape) == 2 and waveform.shape[0] == 2:
                # Transpose if channels are first dimension
                waveform = waveform.T
            
            self.logger.info(f"Audio shape: {waveform.shape}, Sample rate: {sample_rate}")
            
            # Use temporary directory for Spleeter output
            with tempfile.TemporaryDirectory() as temp_dir:
                # Separate audio using Spleeter
                self.logger.info("Starting audio separation with Spleeter...")
                prediction = self.separator.separate(waveform)
                
                # Extract vocals
                vocals = prediction['vocals']
                accompaniment = prediction['accompaniment']
                
                # Save vocals to final location
                vocals_filename = f"{title}_vocals.wav"
                # Sanitize filename
                vocals_filename = self._sanitize_filename(vocals_filename)
                vocals_path = os.path.join(task_output_dir, vocals_filename)
                
                # Save vocals as WAV file
                sf.write(vocals_path, vocals, sample_rate)
                
                # Optionally save accompaniment (background music)
                accompaniment_filename = f"{title}_accompaniment.wav"
                accompaniment_filename = self._sanitize_filename(accompaniment_filename)
                accompaniment_path = os.path.join(task_output_dir, accompaniment_filename)
                sf.write(accompaniment_path, accompaniment, sample_rate)
                
                self.logger.info(f"Audio processing completed. Vocals saved to: {vocals_path}")
                
                # Verify the output file exists and has content
                if os.path.exists(vocals_path) and os.path.getsize(vocals_path) > 0:
                    return True, vocals_path, ""
                else:
                    return False, "", "فشل في إنشاء ملف الصوت المعالج"
                    
        except Exception as e:
            error_msg = f"خطأ في معالجة الصوت: {str(e)}"
            self.logger.error(error_msg)
            return False, "", error_msg
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename to remove invalid characters"""
        import re
        # Remove or replace invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Remove extra spaces and dots
        filename = re.sub(r'\s+', ' ', filename).strip()
        filename = re.sub(r'\.+', '.', filename)
        # Ensure it's not too long
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
        return filename
    
    def get_audio_info(self, file_path: str) -> Optional[dict]:
        """Get audio file information"""
        if not LIBROSA_AVAILABLE:
            return {
                'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
            }

        try:
            duration = librosa.get_duration(filename=file_path)
            sample_rate = librosa.get_samplerate(file_path)

            return {
                'duration': duration,
                'sample_rate': sample_rate,
                'file_size': os.path.getsize(file_path)
            }
        except Exception as e:
            self.logger.error(f"Failed to get audio info: {e}")
            return None
    
    def cleanup_task_files(self, task_id: str):
        """Clean up all files for a specific task"""
        try:
            task_dir = os.path.join(self.output_dir, task_id)
            if os.path.exists(task_dir):
                shutil.rmtree(task_dir)
                self.logger.info(f"Cleaned up files for task: {task_id}")
        except Exception as e:
            self.logger.error(f"Failed to cleanup task files: {e}")
    
    def validate_audio_file(self, file_path: str) -> bool:
        """Validate if file is a valid audio file"""
        try:
            if not os.path.exists(file_path):
                return False

            if not LIBROSA_AVAILABLE:
                # Basic validation - check file extension
                valid_extensions = ['.wav', '.mp3', '.m4a', '.flac', '.ogg']
                return any(file_path.lower().endswith(ext) for ext in valid_extensions)

            # Try to load a small portion of the file
            librosa.load(file_path, duration=1.0)
            return True
        except Exception:
            return False
