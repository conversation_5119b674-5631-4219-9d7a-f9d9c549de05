import os
import logging
import yt_dlp
from typing import Optional, Tuple
import re

class YouTubeDownloader:
    def __init__(self, output_dir: str = "data/temp"):
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
    
    def extract_video_id(self, url: str) -> Optional[str]:
        """Extract YouTube video ID from URL"""
        patterns = [
            r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})',
            r'youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def get_video_info(self, url: str) -> Optional[dict]:
        """Get video information without downloading"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                return {
                    'id': info.get('id'),
                    'title': info.get('title'),
                    'duration': info.get('duration'),
                    'uploader': info.get('uploader'),
                    'view_count': info.get('view_count'),
                }
        except Exception as e:
            self.logger.error(f"Failed to extract video info: {e}")
            return None
    
    def download_audio(self, url: str, task_id: str) -> Tuple[bool, str, str]:
        """
        Download audio from YouTube video
        Returns: (success, output_file_path, error_message)
        """
        try:
            # Create task-specific directory
            task_dir = os.path.join(self.output_dir, task_id)
            os.makedirs(task_dir, exist_ok=True)
            
            # Get video info first
            video_info = self.get_video_info(url)
            if not video_info:
                return False, "", "فشل في الحصول على معلومات الفيديو"
            
            # Sanitize filename
            title = video_info.get('title', 'unknown')
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', title)
            output_filename = f"{safe_title}.%(ext)s"
            output_path = os.path.join(task_dir, output_filename)
            
            # yt-dlp options for audio download
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': output_path,
                'extractaudio': True,
                'audioformat': 'wav',
                'audioquality': '192K',
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'wav',
                    'preferredquality': '192',
                }],
                'quiet': True,
                'no_warnings': True,
            }
            
            # Download the audio
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            # Find the downloaded file
            downloaded_file = None
            for file in os.listdir(task_dir):
                if file.endswith('.wav'):
                    downloaded_file = os.path.join(task_dir, file)
                    break
            
            if downloaded_file and os.path.exists(downloaded_file):
                self.logger.info(f"Successfully downloaded audio: {downloaded_file}")
                return True, downloaded_file, ""
            else:
                return False, "", "لم يتم العثور على الملف المحمل"
                
        except Exception as e:
            error_msg = f"خطأ في تحميل الصوت: {str(e)}"
            self.logger.error(error_msg)
            return False, "", error_msg
    
    def cleanup_temp_files(self, task_id: str):
        """Clean up temporary files for a task"""
        try:
            task_dir = os.path.join(self.output_dir, task_id)
            if os.path.exists(task_dir):
                for file in os.listdir(task_dir):
                    file_path = os.path.join(task_dir, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                os.rmdir(task_dir)
                self.logger.info(f"Cleaned up temp files for task: {task_id}")
        except Exception as e:
            self.logger.error(f"Failed to cleanup temp files: {e}")
    
    def validate_url(self, url: str) -> bool:
        """Validate if URL is a valid YouTube URL"""
        youtube_patterns = [
            r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)',
            r'youtube\.com\/watch\?.*v=',
        ]
        
        for pattern in youtube_patterns:
            if re.search(pattern, url):
                return True
        
        return False
