import uuid
import logging
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, field
import os
import json

# Try to import PyQt5, make it optional
try:
    from PyQt5.QtCore import QObject, pyqtSignal
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False
    # Create dummy classes for when PyQt5 is not available
    class QObject:
        def __init__(self):
            pass

    def pyqtSignal(*args, **kwargs):
        def dummy_signal(*args, **kwargs):
            pass
        return dummy_signal

class TaskStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class Task:
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    url: str = ""
    title: str = ""
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    error_message: str = ""
    output_folder: str = ""
    vocals_file: str = ""
    
    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'url': self.url,
            'title': self.title,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_message': self.error_message,
            'output_folder': self.output_folder,
            'vocals_file': self.vocals_file
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Task':
        task = cls()
        task.id = data.get('id', str(uuid.uuid4()))
        task.url = data.get('url', '')
        task.title = data.get('title', '')
        task.status = TaskStatus(data.get('status', TaskStatus.PENDING.value))
        task.created_at = datetime.fromisoformat(data.get('created_at', datetime.now().isoformat()))
        task.completed_at = datetime.fromisoformat(data['completed_at']) if data.get('completed_at') else None
        task.error_message = data.get('error_message', '')
        task.output_folder = data.get('output_folder', '')
        task.vocals_file = data.get('vocals_file', '')
        return task

class TaskManager(QObject):
    # Signals for GUI updates
    task_added = pyqtSignal(str)  # task_id
    task_updated = pyqtSignal(str)  # task_id
    task_removed = pyqtSignal(str)  # task_id
    
    def __init__(self, data_dir: str = "data"):
        super().__init__()
        self.data_dir = data_dir
        self.tasks_file = os.path.join(data_dir, "tasks.json")
        self.tasks: Dict[str, Task] = {}
        self.logger = logging.getLogger(__name__)
        
        # Create data directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)
        
        # Load existing tasks
        self.load_tasks()
    
    def add_task(self, url: str, title: str = "") -> str:
        """Add a new task and return its ID"""
        task = Task(url=url, title=title or url)
        self.tasks[task.id] = task
        self.save_tasks()
        self.emit_signal('task_added', task.id)
        self.logger.info(f"Task added: {task.id} - {task.url}")
        return task.id
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[Task]:
        """Get all tasks sorted by creation time (newest first)"""
        return sorted(self.tasks.values(), key=lambda t: t.created_at, reverse=True)
    
    def update_task_status(self, task_id: str, status: TaskStatus, error_message: str = ""):
        """Update task status"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.status = status
            task.error_message = error_message
            
            if status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                task.completed_at = datetime.now()
            
            self.save_tasks()
            self.emit_signal('task_updated', task_id)
            self.logger.info(f"Task {task_id} status updated to {status.value}")
    
    def update_task_title(self, task_id: str, title: str):
        """Update task title"""
        if task_id in self.tasks:
            self.tasks[task_id].title = title
            self.save_tasks()
            self.emit_signal('task_updated', task_id)
    
    def update_task_output(self, task_id: str, output_folder: str, vocals_file: str = ""):
        """Update task output information"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.output_folder = output_folder
            task.vocals_file = vocals_file
            self.save_tasks()
            self.emit_signal('task_updated', task_id)
    
    def remove_task(self, task_id: str):
        """Remove a task"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            self.save_tasks()
            self.emit_signal('task_removed', task_id)
            self.logger.info(f"Task removed: {task_id}")
    
    def emit_signal(self, signal_name: str, *args):
        """Emit signal if PyQt5 is available"""
        if PYQT5_AVAILABLE:
            signal = getattr(self, signal_name, None)
            if signal:
                signal.emit(*args)

    def save_tasks(self):
        """Save tasks to file"""
        try:
            tasks_data = {task_id: task.to_dict() for task_id, task in self.tasks.items()}
            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save tasks: {e}")

    def load_tasks(self):
        """Load tasks from file"""
        try:
            if os.path.exists(self.tasks_file):
                with open(self.tasks_file, 'r', encoding='utf-8') as f:
                    tasks_data = json.load(f)

                for task_id, task_dict in tasks_data.items():
                    self.tasks[task_id] = Task.from_dict(task_dict)

                self.logger.info(f"Loaded {len(self.tasks)} tasks")
        except Exception as e:
            self.logger.error(f"Failed to load tasks: {e}")
            self.tasks = {}
