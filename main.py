#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
No Music Desktop Application
============================

برنامج سطح مكتب لإزالة الموسيقى من فيديوهات YouTube
يعمل كأداة مستقلة وكخادم API لإضافة المتصفح

المطور: Assistant
الإصدار: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_environment():
    """Setup the application environment"""
    # Create necessary directories
    directories = [
        "data/temp",
        "data/processed", 
        "logs",
        "gui",
        "server",
        "processing",
        "tasks"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # Setup basic logging
    log_file = os.path.join("logs", "app.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("No Music Desktop Application Starting...")
    logger.info("=" * 50)
    
    return logger

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'PyQt5',
        'fastapi',
        'uvicorn',
        'yt_dlp',
        'spleeter',
        'tensorflow',
        'librosa',
        'soundfile',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ المكتبات التالية مفقودة:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nيرجى تثبيت المكتبات المفقودة باستخدام:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ جميع المكتبات المطلوبة متوفرة")
    return True

def main():
    """Main application entry point"""
    try:
        # Setup environment
        logger = setup_environment()
        
        # Check dependencies
        if not check_dependencies():
            sys.exit(1)
        
        # Import GUI module (after environment setup)
        try:
            from gui.gui import main as gui_main
        except ImportError as e:
            logger.error(f"Failed to import GUI module: {e}")
            print("❌ فشل في تحميل واجهة المستخدم")
            print("تأكد من وجود جميع الملفات المطلوبة")
            sys.exit(1)
        
        # Start the GUI application
        logger.info("Starting GUI application...")
        exit_code = gui_main()
        
        logger.info(f"Application exited with code: {exit_code}")
        return exit_code
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف البرنامج بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        logging.exception("Unexpected error occurred")
        return 1

if __name__ == "__main__":
    sys.exit(main())
